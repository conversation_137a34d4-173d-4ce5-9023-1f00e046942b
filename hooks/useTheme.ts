import { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

export function useTheme() {
  const [isDark, setIsDark] = useState(false);

  useEffect(() => {
    loadTheme();
  }, []);

  const loadTheme = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem('app-theme');
      if (savedTheme) {
        setIsDark(savedTheme === 'dark');
      }
    } catch (error) {
      console.log('Error loading theme', error);
    }
  };

  const toggleTheme = async () => {
    const newTheme = !isDark;
    setIsDark(newTheme);
    try {
      await AsyncStorage.setItem('app-theme', newTheme ? 'dark' : 'light');
    } catch (error) {
      console.log('Error saving theme', error);
    }
  };

  // Get the actual background color value
  const backgroundColor = isDark ? '#03305e' : '#b6d9fc';

  return {
    isDark,
    toggleTheme,
    themeName: isDark ? 'dark' : 'light',
    backgroundColor,
  };
}