import { useState, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Create a global state for theme that can be shared across the app
let globalThemeState = {
  isDark: false,
  listeners: new Set<(isDark: boolean) => void>(),
};

export function useTheme() {
  const [isDark, setIsDark] = useState(globalThemeState.isDark);

  useEffect(() => {
    loadTheme();

    // Add this component as a listener for theme changes
    const listener = (newIsDark: boolean) => {
      setIsDark(newIsDark);
    };
    globalThemeState.listeners.add(listener);

    // Cleanup listener on unmount
    return () => {
      globalThemeState.listeners.delete(listener);
    };
  }, []);

  const loadTheme = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem('app-theme');
      if (savedTheme) {
        const newIsDark = savedTheme === 'dark';
        updateGlobalTheme(newIsDark);
      }
    } catch (error) {
      console.log('Error loading theme', error);
    }
  };

  const updateGlobalTheme = (newIsDark: boolean) => {
    globalThemeState.isDark = newIsDark;
    // Notify all listeners of the theme change
    globalThemeState.listeners.forEach(listener => listener(newIsDark));
  };

  const toggleTheme = useCallback(async () => {
    const newTheme = !globalThemeState.isDark;
    updateGlobalTheme(newTheme);
    try {
      await AsyncStorage.setItem('app-theme', newTheme ? 'dark' : 'light');
    } catch (error) {
      console.log('Error saving theme', error);
    }
  }, []);

  // Get the actual background color value
  const backgroundColor = isDark ? '#03305e' : '#b6d9fc';

  return {
    isDark,
    toggleTheme,
    themeName: isDark ? 'dark' : 'light',
    backgroundColor,
  };
}