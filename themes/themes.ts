import { createThemes, defaultComponentThemes } from '@tamagui/theme-builder'
import * as Colors from '@tamagui/colors'

const darkPalette = ['hsla(210, 94%, 19%, 1)','hsla(210, 94%, 22%, 1)','hsla(210, 94%, 26%, 1)','hsla(210, 94%, 29%, 1)','hsla(210, 94%, 33%, 1)','hsla(210, 94%, 36%, 1)','hsla(210, 94%, 40%, 1)','hsla(210, 94%, 43%, 1)','hsla(210, 94%, 47%, 1)','hsla(210, 94%, 50%, 1)','hsla(210, 94%, 93%, 1)','hsla(210, 94%, 99%, 1)']
const lightPalette = ['hsla(210, 94%, 89%, 1)','hsla(210, 94%, 85%, 1)','hsla(210, 94%, 81%, 1)','hsla(210, 94%, 76%, 1)','hsla(210, 94%, 72%, 1)','hsla(210, 94%, 68%, 1)','hsla(210, 94%, 63%, 1)','hsla(210, 94%, 59%, 1)','hsla(210, 94%, 54%, 1)','hsla(210, 94%, 50%, 1)','hsla(210, 94%, 15%, 1)','hsla(210, 94%, 19%, 1)']

const lightShadows = {
  shadow1: 'rgba(0,0,0,0.04)',
  shadow2: 'rgba(0,0,0,0.08)',
  shadow3: 'rgba(0,0,0,0.16)',
  shadow4: 'rgba(0,0,0,0.24)',
  shadow5: 'rgba(0,0,0,0.32)',
  shadow6: 'rgba(0,0,0,0.4)',
}

const darkShadows = {
  shadow1: 'rgba(0,0,0,0.2)',
  shadow2: 'rgba(0,0,0,0.3)',
  shadow3: 'rgba(0,0,0,0.4)',
  shadow4: 'rgba(0,0,0,0.5)',
  shadow5: 'rgba(0,0,0,0.6)',
  shadow6: 'rgba(0,0,0,0.7)',
}

// we're adding some example sub-themes for you to show how they are done, "success" "warning", "error":

const builtThemes = createThemes({
  componentThemes: defaultComponentThemes,

  base: {
    palette: {
      dark: darkPalette,
      light: lightPalette,
    },

    extra: {
      light: {
        ...Colors.green,
        ...Colors.red,
        ...Colors.yellow,
        ...lightShadows,
        shadowColor: lightShadows.shadow1,
        background: '#b6d9fc',
      },
      dark: {
        ...Colors.greenDark,
        ...Colors.redDark,
        ...Colors.yellowDark,
        ...darkShadows,
        shadowColor: darkShadows.shadow1,
        background: '#03305e',
      },
    },
  },

  accent: {
    palette: {
      dark: ['hsla(209, 77%, 12%, 1)','hsla(209, 77%, 17%, 1)','hsla(209, 77%, 23%, 1)','hsla(209, 77%, 28%, 1)','hsla(209, 77%, 33%, 1)','hsla(209, 77%, 39%, 1)','hsla(209, 77%, 44%, 1)','hsla(209, 77%, 49%, 1)','hsla(209, 77%, 55%, 1)','hsla(209, 77%, 60%, 1)','hsla(250, 50%, 90%, 1)','hsla(250, 50%, 95%, 1)'],
      light: ['hsla(209, 77%, 53%, 1)','hsla(209, 77%, 54%, 1)','hsla(209, 77%, 56%, 1)','hsla(209, 77%, 57%, 1)','hsla(209, 77%, 58%, 1)','hsla(209, 77%, 60%, 1)','hsla(209, 77%, 61%, 1)','hsla(209, 77%, 62%, 1)','hsla(209, 77%, 64%, 1)','hsla(209, 77%, 65%, 1)','hsla(250, 50%, 95%, 1)','hsla(250, 50%, 95%, 1)'],
    },
  },

  childrenThemes: {
    warning: {
      palette: {
        dark: Object.values(Colors.yellowDark),
        light: Object.values(Colors.yellow),
      },
    },

    error: {
      palette: {
        dark: Object.values(Colors.redDark),
        light: Object.values(Colors.red),
      },
    },

    success: {
      palette: {
        dark: Object.values(Colors.greenDark),
        light: Object.values(Colors.green),
      },
    },
  },

  // optionally add more, can pass palette or template

  // grandChildrenThemes: {
  //   alt1: {
  //     template: 'alt1',
  //   },
  //   alt2: {
  //     template: 'alt2',
  //   },
  //   surface1: {
  //     template: 'surface1',
  //   },
  //   surface2: {
  //     template: 'surface2',
  //   },
  //   surface3: {
  //     template: 'surface3',
  //   },
  // },
})

export type Themes = typeof builtThemes

// the process.env conditional here is optional but saves web client-side bundle
// size by leaving out themes JS. tamagui automatically hydrates themes from CSS
// back into JS for you, and the bundler plugins set TAMAGUI_ENVIRONMENT. so
// long as you are using the Vite, Next, Webpack plugins this should just work,
// but if not you can just export builtThemes directly as themes:
export const themes: Themes = builtThemes as any
