{"name": "youlogy-app", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@tamagui/animations-react-native": "^1.132.20", "@tamagui/config": "^1.132.20", "@tamagui/core": "^1.132.20", "@tamagui/dialog": "^1.132.20", "@tamagui/font-inter": "^1.132.20", "@tamagui/portal": "^1.132.20", "@tamagui/shorthands": "^1.132.20", "@tamagui/theme-base": "^1.132.20", "expo": "^53.0.0", "expo-constants": "~17.1.3", "expo-font": "~13.2.2", "expo-linking": "~7.1.3", "expo-router": "~5.0.2", "expo-splash-screen": "~0.30.6", "expo-status-bar": "~2.2.2", "expo-system-ui": "~5.0.5", "expo-web-browser": "~14.1.5", "i18next": "^25.3.6", "lucide-react-native": "^0.540.0", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.6.1", "react-native": "0.79.1", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.3.0", "react-native-screens": "~4.10.0", "react-native-svg": "^15.11.2", "react-native-web": "^0.20.0", "tamagui": "^1.132.20"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}}