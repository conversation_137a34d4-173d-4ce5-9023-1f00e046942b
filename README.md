# Youlogy

A React Native mobile application built with Expo and Tamagui for managing digital memories and legacy content.

## Overview

Youlogy is a digital memory and legacy management platform that allows users to:

- **Create and manage digital memories** - Store photos, videos, and other digital assets
- **Share with trusted contacts** - Organize and share memories with family and friends
- **Plan digital legacy** - Set up time capsules and memorial settings for future sharing
- **Subscription-based storage** - Multiple tiers from free to family plans
- **Contact management** - Invite and manage custodians and members
- **Group organization** - Create groups for different circles (family, friends, work)

## Features

### Core Functionality
- **Dashboard** - Overview of your memories, recent activity, and quick actions
- **Library** - Manage your digital assets (photos, videos, documents)
- **Contacts** - Invite and manage users, assign roles (custodians, members)
- **Youlogy** - Your main memory collections and subscription management
- **Profile** - Account settings and preferences

### Key Features
- **Memory Collections** - Organize content into shareable collections
- **Time Capsules** - Schedule content to be shared in the future
- **Memorial Settings** - Configure digital legacy preferences
- **Multi-tier Subscriptions** - Free, Standard ($5), Premium ($10), Family ($30)
- **Contact Roles** - Custodians and members with different permissions
- **Group Management** - Organize contacts into groups
- **Internationalization** - Multi-language support with i18next

## Tech Stack

- **Framework**: React Native with Expo
- **UI Library**: Tamagui for cross-platform UI components
- **Navigation**: Expo Router with file-based routing
- **Styling**: Tamagui theming system with light/dark mode support
- **Icons**: Material Icons via @expo/vector-icons
- **Internationalization**: i18next and react-i18next
- **State Management**: React hooks and context
- **Storage**: AsyncStorage for local data persistence

## Project Structure

```
app/
├── (auth)/          # Authentication screens
│   ├── login.tsx
│   ├── register.tsx
│   ├── forgot-password.tsx
│   └── reset-password.tsx
├── (onboarding)/    # Onboarding flow
│   └── welcome.tsx
├── (tabs)/          # Main app tabs
│   ├── index.tsx    # Dashboard
│   ├── library.tsx  # Asset management
│   ├── contacts.tsx # Contact management
│   ├── youlogy.tsx  # Memory collections
│   └── profile.tsx  # User profile
├── _layout.tsx      # Root layout
└── +not-found.tsx   # 404 page

hooks/               # Custom React hooks
├── useFrameworkReady.ts
└── useTheme.ts

i18n/               # Internationalization
├── index.ts
└── locales/

themes/             # Tamagui theme configuration
└── themes.ts

assets/             # Static assets
└── images/
```

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- npm or yarn
- Expo CLI
- iOS Simulator (for iOS development)
- Android Studio/Emulator (for Android development)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd youlogy-wireframe
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open the app:
   - Scan the QR code with Expo Go app on your device
   - Press `i` for iOS simulator
   - Press `a` for Android emulator

### Available Scripts

- `npm run dev` - Start the development server
- `npm run build:web` - Build for web deployment
- `npm run lint` - Run ESLint

## Configuration

### Theme Configuration
The app uses Tamagui for theming with support for light and dark modes. Theme configuration is in `tamagui.config.ts`.

### Internationalization
Language files are located in `i18n/locales/`. The app currently supports multiple languages through i18next.

## Subscription Plans

| Plan | Photos | Videos | Storage | Price |
|------|--------|--------|---------|-------|
| Free | 5 | 1 | 0.04 GB | $0 |
| Standard | 30 | 10 | 0.15 GB | $5 |
| Premium | 100 | 20 | 0.8 GB | $10 |
| Family | 500 | 100 | 4.0 GB | $30 |

## Development

This is a wireframe/prototype application built with Expo and React Native. The app demonstrates the core user interface and navigation flow for a digital memory management platform.

### Key Components
- Cross-platform compatibility (iOS, Android, Web)
- Responsive design with Tamagui
- File-based routing with Expo Router
- Theme switching (light/dark mode)
- Internationalization ready

## License

Private project - All rights reserved.
