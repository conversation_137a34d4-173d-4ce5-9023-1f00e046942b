import { Link, Stack } from 'expo-router';
import { YStack, Text } from 'tamagui';
import { useTheme } from '@/hooks/useTheme';

export default function NotFoundScreen() {
  const { backgroundColor } = useTheme();

  return (
    <>
      <Stack.Screen options={{ title: 'Oops!' }} />
      <YStack flex={1} alignItems="center" justifyContent="center" padding="$4" backgroundColor={backgroundColor}>
        <Text fontSize="$6" fontWeight="600" color="$color" textAlign="center">
          This screen doesn't exist.
        </Text>
        <Link href="/" style={{ marginTop: 15, paddingVertical: 15 }}>
          <Text color="$primary" textDecorationLine="underline">
            Go to home screen!
          </Text>
        </Link>
      </YStack>
    </>
  );
}
