import { Link, Stack } from 'expo-router';
import { Text } from 'react-native';
import { YStack } from 'tamagui';

export default function NotFoundScreen() {
  return (
    <>
      <Stack.Screen options={{ title: 'Oops!' }} />
      <YStack flex={1} alignItems="center" justifyContent="center" padding="$4" backgroundColor="$background">
        <Text style={{ fontSize: 20, fontWeight: '600', color: '$color' }}>
          This screen doesn't exist.
        </Text>
        <Link href="/" style={{ marginTop: 15, paddingVertical: 15 }}>
          <Text style={{ color: '$primary' }}>Go to home screen!</Text>
        </Link>
      </YStack>
    </>
  );
}
