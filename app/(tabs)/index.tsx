import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { YStack, XStack, Text, Card, ScrollView } from 'tamagui';
import { MaterialIcons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';

export default function DashboardScreen() {
  const { t } = useTranslation();

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '$background' }}>
      <ScrollView flex={1}>
        <YStack padding="$4" space="$4">
          {/* Header */}
          <XStack alignItems="center" justifyContent="space-between" marginBottom="$4">
            <XStack alignItems="center" space="$3">
              <MaterialIcons name="account-circle" size={40} color="#0A88AF" />
              <Text fontSize="$6" fontWeight="bold" color="$primary">
                {t('app.name')}
              </Text>
            </XStack>
            <MaterialIcons name="notifications" size={24} color="#0A88AF" />
          </XStack>

          {/* Welcome Message */}
          <Card padding="$4" backgroundColor="$backgroundHover">
            <Text fontSize="$6" fontWeight="bold" marginBottom="$2">
              {t('dashboard.welcomeBack')}
            </Text>
            <Text fontSize="$4" color="$colorPress">
              {t('dashboard.welcomeDescription')}
            </Text>
          </Card>

          {/* Quick Actions */}
          <YStack space="$3">
            <Text fontSize="$5" fontWeight="bold">{t('dashboard.quickActions')}</Text>
            
            <XStack space="$3">
              <Card flex={1} padding="$4" pressStyle={{ scale: 0.97 }}>
                <YStack alignItems="center" space="$2">
                  <MaterialIcons name="folder" size={32} color="#0A88AF" />
                  <Text fontSize="$4" textAlign="center">{t('dashboard.yourAssets')}</Text>
                  <Text fontSize="$2" color="$colorPress" textAlign="center">
                    12 {t('dashboard.items')}
                  </Text>
                </YStack>
              </Card>

              <Card flex={1} padding="$4" pressStyle={{ scale: 0.97 }}>
                <YStack alignItems="center" space="$2">
                  <MaterialIcons name="people" size={32} color="#0A88AF" />
                  <Text fontSize="$4" textAlign="center">{t('dashboard.yourContacts')}</Text>
                  <Text fontSize="$2" color="$colorPress" textAlign="center">
                    8 {t('dashboard.contacts')}
                  </Text>
                </YStack>
              </Card>
            </XStack>

            <XStack space="$3">
              <Card flex={1} padding="$4" pressStyle={{ scale: 0.97 }}>
                <YStack alignItems="center" space="$2">
                  <MaterialIcons name="timeline" size={32} color="#0A88AF" />
                  <Text fontSize="$4" textAlign="center">{t('dashboard.yourYoulogy')}</Text>
                  <Text fontSize="$2" color="$colorPress" textAlign="center">
                    5 {t('dashboard.memories')}
                  </Text>
                </YStack>
              </Card>

              <Card flex={1} padding="$4" pressStyle={{ scale: 0.97 }}>
                <YStack alignItems="center" space="$2">
                  <MaterialIcons name="settings" size={32} color="#0A88AF" />
                  <Text fontSize="$4" textAlign="center">{t('dashboard.profileSettings')}</Text>
                  <Text fontSize="$2" color="$colorPress" textAlign="center">
                    {t('dashboard.manage')}
                  </Text>
                </YStack>
              </Card>
            </XStack>
          </YStack>

          {/* Recent Activity */}
          <YStack space="$3">
            <Text fontSize="$5" fontWeight="bold">{t('dashboard.recentActivity')}</Text>
            
            <Card padding="$4">
              <XStack alignItems="center" space="$3" marginBottom="$3">
                <MaterialIcons name="photo" size={24} color="#5889B6" />
                <YStack flex={1}>
                  <Text fontSize="$4">{t('dashboard.photoUploaded')}</Text>
                  <Text fontSize="$3" color="$colorPress">2 {t('dashboard.hoursAgo')}</Text>
                </YStack>
              </XStack>

              <XStack alignItems="center" space="$3" marginBottom="$3">
                <MaterialIcons name="person-add" size={24} color="#5889B6" />
                <YStack flex={1}>
                  <Text fontSize="$4">{t('dashboard.newContactAdded')}</Text>
                  <Text fontSize="$3" color="$colorPress">1 {t('dashboard.dayAgo')}</Text>
                </YStack>
              </XStack>

              <XStack alignItems="center" space="$3">
                <MaterialIcons name="video-library" size={24} color="#5889B6" />
                <YStack flex={1}>
                  <Text fontSize="$4">{t('dashboard.videoShared')}</Text>
                  <Text fontSize="$3" color="$colorPress">3 {t('dashboard.daysAgo')}</Text>
                </YStack>
              </XStack>
            </Card>
          </YStack>
        </YStack>
      </ScrollView>
    </SafeAreaView>
  );
}