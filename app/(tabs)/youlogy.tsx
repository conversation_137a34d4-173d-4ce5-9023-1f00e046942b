import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { YStack, XStack, Text, Card, Button, ScrollView } from 'tamagui';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/hooks/useTheme';

const subscriptionPlans = [
  { name: 'Free', photos: 5, videos: 1, storage: '0.04 GB', price: '$0' },
  { name: 'Standard', photos: 30, videos: 10, storage: '0.15 GB', price: '$5' },
  { name: 'Premium', photos: 100, videos: 20, storage: '0.8 GB', price: '$10' },
  { name: 'Family', photos: 500, videos: 100, storage: '4.0 GB', price: '$30' },
];

export default function YoulogyScreen() {
  const { backgroundColor } = useTheme();

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor }}>
      <ScrollView flex={1}>
        <YStack padding="$4" space="$4">
          {/* Header */}
          <XStack alignItems="center" justifyContent="space-between">
            <Text fontSize="$6" fontWeight="bold" color="$primary">
              Your Youlogy
            </Text>
            <Button size="$3" theme="active">
              <XStack alignItems="center" space="$2">
                <MaterialIcons name="add" size={20} color="white" />
                <Text color="white">Create</Text>
              </XStack>
            </Button>
          </XStack>

          {/* Current Plan */}
          <Card padding="$4" backgroundColor="$primary">
            <Text fontSize="$5" fontWeight="bold" color="white" marginBottom="$2">
              Current Plan: Standard
            </Text>
            <Text fontSize="$3" color="white" opacity={0.9}>
              30 photos • 10 videos • 0.15 GB storage
            </Text>
          </Card>

          {/* Youlogy Content */}
          <YStack space="$3">
            <Text fontSize="$5" fontWeight="bold">Your Memories</Text>
            
            {[1, 2, 3].map((item) => (
              <Card key={item} padding="$4" pressStyle={{ scale: 0.98 }}>
                <XStack alignItems="center" space="$3">
                  <MaterialIcons name="timeline" size={40} color="#5889B6" />
                  <YStack flex={1}>
                    <Text fontSize="$4" fontWeight="bold">
                      Memory Collection {item}
                    </Text>
                    <Text fontSize="$3" color="$colorPress">
                      5 photos, 2 videos
                    </Text>
                    <Text fontSize="$3" color="$colorPress">
                      Shared with 2 contacts
                    </Text>
                    <Text fontSize="$2" color="$colorPress">
                      Updated 1 week ago
                    </Text>
                  </YStack>
                  <XStack space="$2">
                    <Button size="$2" chromeless>
                      <MaterialIcons name="share" size={20} color="#0A88AF" />
                    </Button>
                    <Button size="$2" chromeless>
                      <MaterialIcons name="edit" size={20} color="#0A88AF" />
                    </Button>
                  </XStack>
                </XStack>
              </Card>
            ))}
          </YStack>

          {/* Subscription Plans */}
          <YStack space="$3">
            <XStack alignItems="center" justifyContent="space-between">
              <Text fontSize="$5" fontWeight="bold">Upgrade Plan</Text>
              <Button size="$2" chromeless>
                <Text color="$primary">Compare All</Text>
              </Button>
            </XStack>

            {subscriptionPlans.map((plan, index) => (
              <Card 
                key={index} 
                padding="$4" 
                pressStyle={{ scale: 0.98 }}
                backgroundColor={plan.name === 'Standard' ? '$backgroundPress' : '$background'}
              >
                <XStack alignItems="center" justifyContent="space-between">
                  <YStack flex={1}>
                    <XStack alignItems="center" space="$2">
                      <Text fontSize="$4" fontWeight="bold">
                        {plan.name}
                      </Text>
                      {plan.name === 'Standard' && (
                        <Text fontSize="$2" color="$primary" fontWeight="bold">
                          CURRENT
                        </Text>
                      )}
                    </XStack>
                    <Text fontSize="$3" color="$colorPress">
                      {plan.photos} photos • {plan.videos} videos
                    </Text>
                    <Text fontSize="$3" color="$colorPress">
                      {plan.storage} storage
                    </Text>
                  </YStack>
                  <XStack alignItems="center" space="$3">
                    <Text fontSize="$4" fontWeight="bold" color="$primary">
                      {plan.price}
                    </Text>
                    {plan.name !== 'Standard' && (
                      <Button size="$2" theme="active">
                        {plan.name === 'Free' ? 'Downgrade' : 'Upgrade'}
                      </Button>
                    )}
                  </XStack>
                </XStack>
              </Card>
            ))}
          </YStack>

          {/* Legacy Features */}
          <YStack space="$3">
            <Text fontSize="$5" fontWeight="bold">Legacy Features</Text>
            
            <Card padding="$4">
              <XStack alignItems="center" space="$3">
                <MaterialIcons name="schedule" size={32} color="#5889B6" />
                <YStack flex={1}>
                  <Text fontSize="$4" fontWeight="bold">
                    Time Capsules
                  </Text>
                  <Text fontSize="$3" color="$colorPress">
                    Schedule content to be shared in the future
                  </Text>
                </YStack>
                <Button size="$2" chromeless>
                  <MaterialIcons name="arrow-forward" size={20} color="#0A88AF" />
                </Button>
              </XStack>
            </Card>

            <Card padding="$4">
              <XStack alignItems="center" space="$3">
                <MaterialIcons name="favorite" size={32} color="#5889B6" />
                <YStack flex={1}>
                  <Text fontSize="$4" fontWeight="bold">
                    Memorial Settings
                  </Text>
                  <Text fontSize="$3" color="$colorPress">
                    Configure your digital legacy preferences
                  </Text>
                </YStack>
                <Button size="$2" chromeless>
                  <MaterialIcons name="arrow-forward" size={20} color="#0A88AF" />
                </Button>
              </XStack>
            </Card>
          </YStack>
        </YStack>
      </ScrollView>
    </SafeAreaView>
  );
}