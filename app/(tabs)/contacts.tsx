import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { YStack, XStack, Text, Card, Button, Input, ScrollView, Dialog, Adapt, Sheet } from 'tamagui';
import { MaterialIcons } from '@expo/vector-icons';

export default function ContactsScreen() {
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [formData, setFormData] = React.useState({
    firstName: '',
    lastName: '',
    email: '',
    contactNumber: ''
  });

  const handleInvite = () => {
    // In real app, handle invitation logic
    console.log('Inviting user:', formData);
    setDialogOpen(false);
    // Reset form
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      contactNumber: ''
    });
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '$background' }}>
      <ScrollView flex={1}>
        <YStack padding="$4" space="$4">
          {/* Header */}
          <XStack alignItems="center" justifyContent="space-between">
            <Text fontSize="$6" fontWeight="bold" color="$primary">
              Your Contacts
            </Text>
            <XStack space="$2">
              <Dialog modal open={dialogOpen} onOpenChange={setDialogOpen}>
                <Dialog.Trigger asChild>
                  <Button size="$3" theme="active">
                    <XStack alignItems="center" space="$1">
                      <MaterialIcons name="person-add" size={16} color="white" />
                      <Text color="white" fontSize="$3">Invite</Text>
                    </XStack>
                  </Button>
                </Dialog.Trigger>

                <Adapt when="sm" platform="touch">
                  <Sheet animation="medium" zIndex={200000} modal dismissOnSnapToBottom>
                    <Sheet.Frame padding="$4" gap="$4">
                      <Adapt.Contents />
                    </Sheet.Frame>
                    <Sheet.Overlay
                      animation="lazy"
                      enterStyle={{ opacity: 0 }}
                      exitStyle={{ opacity: 0 }}
                    />
                  </Sheet>
                </Adapt>

                <Dialog.Portal>
                  <Dialog.Overlay
                    key="overlay"
                    animation="slow"
                    opacity={0.5}
                    enterStyle={{ opacity: 0 }}
                    exitStyle={{ opacity: 0 }}
                  />

                  <Dialog.Content
                    bordered
                    elevate
                    key="content"
                    animateOnly={['transform', 'opacity']}
                    animation={[
                      'quicker',
                      {
                        opacity: {
                          overshootClamping: true,
                        },
                      },
                    ]}
                    enterStyle={{ x: 0, y: -20, opacity: 0, scale: 0.9 }}
                    exitStyle={{ x: 0, y: 10, opacity: 0, scale: 0.95 }}
                    gap="$4"
                    padding="$4"
                    maxWidth={400}
                  >
                    <Dialog.Title fontSize="$6" fontWeight="bold">
                      Invite New Contact
                    </Dialog.Title>
                    <Dialog.Description fontSize="$4" color="$colorPress">
                      Enter the contact details to send an invitation
                    </Dialog.Description>

                    <YStack space="$4">
                      <XStack space="$2">
                        <YStack flex={1}>
                          <Text fontSize="$3" marginBottom="$2">First Name</Text>
                          <Input
                            placeholder="First name"
                            value={formData.firstName}
                            onChangeText={(text) => setFormData(prev => ({ ...prev, firstName: text }))}
                            size="$4"
                          />
                        </YStack>
                        <YStack flex={1}>
                          <Text fontSize="$3" marginBottom="$2">Last Name</Text>
                          <Input
                            placeholder="Last name"
                            value={formData.lastName}
                            onChangeText={(text) => setFormData(prev => ({ ...prev, lastName: text }))}
                            size="$4"
                          />
                        </YStack>
                      </XStack>

                      <YStack>
                        <Text fontSize="$3" marginBottom="$2">Email</Text>
                        <Input
                          placeholder="Enter email address"
                          value={formData.email}
                          onChangeText={(text) => setFormData(prev => ({ ...prev, email: text }))}
                          keyboardType="email-address"
                          autoCapitalize="none"
                          size="$4"
                        />
                      </YStack>

                      <YStack>
                        <Text fontSize="$3" marginBottom="$2">Contact Number</Text>
                        <Input
                          placeholder="Enter contact number"
                          value={formData.contactNumber}
                          onChangeText={(text) => setFormData(prev => ({ ...prev, contactNumber: text }))}
                          keyboardType="phone-pad"
                          size="$4"
                        />
                      </YStack>
                    </YStack>

                    <XStack alignSelf="flex-end" gap="$4">
                      <Dialog.Close displayWhenAdapted asChild>
                        <Button theme="alt2" aria-label="Close">
                          Cancel
                        </Button>
                      </Dialog.Close>
                      <Button theme="active" onPress={handleInvite} aria-label="Save">
                        Send Invite
                      </Button>
                    </XStack>
                  </Dialog.Content>
                </Dialog.Portal>
              </Dialog>

              <Button size="$3" variant="outlined">
                <XStack alignItems="center" space="$1">
                  <MaterialIcons name="group-add" size={16} color="#0A88AF" />
                  <Text color="$primary" fontSize="$3">Group</Text>
                </XStack>
              </Button>
            </XStack>
          </XStack>

          {/* Search */}
          <Input placeholder="Search contacts..." size="$4" />

          {/* Quick Actions */}
          <XStack space="$3">
            <Card flex={1} padding="$3" pressStyle={{ scale: 0.97 }}>
              <YStack alignItems="center" space="$1">
                <MaterialIcons name="groups" size={24} color="#0A88AF" />
                <Text fontSize="$3">Groups</Text>
                <Text fontSize="$2" color="$colorPress">3 groups</Text>
              </YStack>
            </Card>

            <Card flex={1} padding="$3" pressStyle={{ scale: 0.97 }}>
              <YStack alignItems="center" space="$1">
                <MaterialIcons name="admin-panel-settings" size={24} color="#0A88AF" />
                <Text fontSize="$3">Custodians</Text>
                <Text fontSize="$2" color="$colorPress">2 contacts</Text>
              </YStack>
            </Card>

            <Card flex={1} padding="$3" pressStyle={{ scale: 0.97 }}>
              <YStack alignItems="center" space="$1">
                <MaterialIcons name="pending" size={24} color="#0A88AF" />
                <Text fontSize="$3">Pending</Text>
                <Text fontSize="$2" color="$colorPress">1 invite</Text>
              </YStack>
            </Card>
          </XStack>

          {/* Contacts List */}
          <YStack space="$3">
            <Text fontSize="$5" fontWeight="bold">All Contacts</Text>

            {[
              { name: 'John Doe', email: '<EMAIL>', role: 'Custodian', assets: 5, status: 'Active' },
              { name: 'Jane Smith', email: '<EMAIL>', role: 'Member', assets: 3, status: 'Active' },
              { name: 'Mike Johnson', email: '<EMAIL>', role: 'Member', assets: 0, status: 'Pending' },
            ].map((contact, index) => (
              <Card key={index} padding="$4" pressStyle={{ scale: 0.98 }}>
                <XStack alignItems="center" space="$3">
                  <MaterialIcons name="account-circle" size={40} color="#5889B6" />
                  <YStack flex={1}>
                    <XStack alignItems="center" space="$2">
                      <Text fontSize="$4" fontWeight="bold">
                        {contact.name}
                      </Text>
                      {contact.role === 'Custodian' && (
                        <MaterialIcons name="verified" size={16} color="#0A88AF" />
                      )}
                    </XStack>
                    <Text fontSize="$3" color="$colorPress">
                      {contact.email}
                    </Text>
                    <XStack alignItems="center" space="$3" marginTop="$1">
                      <Text fontSize="$2" color="$colorPress">
                        Role: {contact.role}
                      </Text>
                      <Text fontSize="$2" color="$colorPress">
                        Assets: {contact.assets}
                      </Text>
                      <Text 
                        fontSize="$2" 
                        color={contact.status === 'Active' ? '$primary' : '$accent'}
                      >
                        {contact.status}
                      </Text>
                    </XStack>
                  </YStack>
                  <XStack space="$1">
                    <Button size="$2" chromeless>
                      <MaterialIcons name="edit" size={18} color="#0A88AF" />
                    </Button>
                    <Button size="$2" chromeless>
                      <MaterialIcons name="more-vert" size={18} color="#0A88AF" />
                    </Button>
                  </XStack>
                </XStack>
              </Card>
            ))}
          </YStack>

          {/* Groups */}
          <YStack space="$3">
            <XStack alignItems="center" justifyContent="space-between">
              <Text fontSize="$5" fontWeight="bold">Groups</Text>
              <Button size="$2" chromeless>
                <Text color="$primary">Manage All</Text>
              </Button>
            </XStack>

            {['Family', 'Friends', 'Work'].map((group, index) => (
              <Card key={index} padding="$3" pressStyle={{ scale: 0.98 }}>
                <XStack alignItems="center" space="$3">
                  <MaterialIcons name="group" size={32} color="#5889B6" />
                  <YStack flex={1}>
                    <Text fontSize="$4" fontWeight="bold">
                      {group}
                    </Text>
                    <Text fontSize="$3" color="$colorPress">
                      {Math.floor(Math.random() * 5) + 2} members
                    </Text>
                  </YStack>
                  <Button size="$2" chromeless>
                    <MaterialIcons name="arrow-forward" size={18} color="#0A88AF" />
                  </Button>
                </XStack>
              </Card>
            ))}
          </YStack>
        </YStack>
      </ScrollView>
    </SafeAreaView>
  );
}