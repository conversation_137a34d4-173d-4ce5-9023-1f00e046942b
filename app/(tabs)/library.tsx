import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { YStack, XStack, Text, Card, Button, Input, ScrollView } from 'tamagui';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/hooks/useTheme';

export default function LibraryScreen() {
  const { backgroundColor } = useTheme();

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor }}>
      <ScrollView flex={1}>
        <YStack padding="$4" space="$4">
          {/* Header */}
          <XStack alignItems="center" justifyContent="space-between">
            <Text fontSize="$6" fontWeight="bold" color="$primary">
              Your Assets
            </Text>
            <Button size="$3" theme="active">
              <XStack alignItems="center" space="$2">
                <MaterialIcons name="add" size={20} color="white" />
                <Text color="white">Add Asset</Text>
              </XStack>
            </Button>
          </XStack>

          {/* Search */}
          <Input placeholder="Search assets..." size="$4" />

          {/* Asset Categories */}
          <YStack space="$3">
            <Text fontSize="$5" fontWeight="bold">Categories</Text>
            
            <XStack space="$3">
              <Card flex={1} padding="$4" pressStyle={{ scale: 0.97 }}>
                <YStack alignItems="center" space="$2">
                  <MaterialIcons name="photo" size={32} color="#0A88AF" />
                  <Text fontSize="$4">Photos</Text>
                  <Text fontSize="$3" color="$colorPress">24 items</Text>
                </YStack>
              </Card>

              <Card flex={1} padding="$4" pressStyle={{ scale: 0.97 }}>
                <YStack alignItems="center" space="$2">
                  <MaterialIcons name="video-library" size={32} color="#0A88AF" />
                  <Text fontSize="$4">Videos</Text>
                  <Text fontSize="$3" color="$colorPress">8 items</Text>
                </YStack>
              </Card>
            </XStack>

            <XStack space="$3">
              <Card flex={1} padding="$4" pressStyle={{ scale: 0.97 }}>
                <YStack alignItems="center" space="$2">
                  <MaterialIcons name="description" size={32} color="#0A88AF" />
                  <Text fontSize="$4">Documents</Text>
                  <Text fontSize="$3" color="$colorPress">12 items</Text>
                </YStack>
              </Card>

              <Card flex={1} padding="$4" pressStyle={{ scale: 0.97 }}>
                <YStack alignItems="center" space="$2">
                  <MaterialIcons name="audiotrack" size={32} color="#0A88AF" />
                  <Text fontSize="$4">Audio</Text>
                  <Text fontSize="$3" color="$colorPress">5 items</Text>
                </YStack>
              </Card>
            </XStack>
          </YStack>

          {/* Recent Assets */}
          <YStack space="$3">
            <XStack alignItems="center" justifyContent="space-between">
              <Text fontSize="$5" fontWeight="bold">Recent Assets</Text>
              <Button size="$2" chromeless>
                <Text color="$primary">View All</Text>
              </Button>
            </XStack>

            {[1, 2, 3].map((item) => (
              <Card key={item} padding="$4" pressStyle={{ scale: 0.98 }}>
                <XStack alignItems="center" space="$3">
                  <MaterialIcons name="photo" size={40} color="#5889B6" />
                  <YStack flex={1}>
                    <Text fontSize="$4" fontWeight="bold">
                      Family Photo {item}
                    </Text>
                    <Text fontSize="$3" color="$colorPress">
                      Shared with 3 contacts
                    </Text>
                    <Text fontSize="$2" color="$colorPress">
                      Added 2 days ago
                    </Text>
                  </YStack>
                  <XStack space="$2">
                    <Button size="$2" chromeless>
                      <MaterialIcons name="share" size={20} color="#0A88AF" />
                    </Button>
                    <Button size="$2" chromeless>
                      <MaterialIcons name="edit" size={20} color="#0A88AF" />
                    </Button>
                  </XStack>
                </XStack>
              </Card>
            ))}
          </YStack>

          {/* Storage Usage */}
          <Card padding="$4">
            <Text fontSize="$4" fontWeight="bold" marginBottom="$3">
              Storage Usage
            </Text>
            <XStack alignItems="center" justifyContent="space-between" marginBottom="$2">
              <Text fontSize="$3">Current Plan: Standard</Text>
              <Text fontSize="$3" color="$primary">150 MB / 150 MB</Text>
            </XStack>
            <YStack height={8} backgroundColor="$borderColor" borderRadius="$2">
              <YStack 
                height={8} 
                backgroundColor="$primary" 
                borderRadius="$2"
                width="100%"
              />
            </YStack>
          </Card>
        </YStack>
      </ScrollView>
    </SafeAreaView>
  );
}