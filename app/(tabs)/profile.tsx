import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { YStack, XStack, Text, Card, Button, ScrollView, Switch, Dialog, Adapt, Sheet, Select } from 'tamagui';
import { MaterialIcons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { ChevronDown, Check } from 'lucide-react-native';
import { useTheme } from '@/hooks/useTheme';

export default function ProfileScreen() {
  const { t, i18n } = useTranslation();
  const { isDark, toggleTheme } = useTheme();
  const [languageDialogOpen, setLanguageDialogOpen] = React.useState(false);

  const languages = [
    { code: 'en', name: t('languages.english') },
    { code: 'de', name: t('languages.german') },
    { code: 'es', name: t('languages.spanish') },
  ];

  const handleLanguageChange = (languageCode: string) => {
    i18n.changeLanguage(languageCode);
    setLanguageDialogOpen(false);
  };

  const getCurrentLanguageName = () => {
    const currentLang = languages.find(lang => lang.code === i18n.language);
    return currentLang?.name || t('languages.english');
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '$background' }}>
      <ScrollView flex={1}>
        <YStack padding="$4" space="$4">
          {/* Profile Header */}
          <Card padding="$4" alignItems="center" space="$3">
            <MaterialIcons name="account-circle" size={80} color="#0A88AF" />
            <Text fontSize="$6" fontWeight="bold" color="$primary">
              John Doe
            </Text>
            <Text fontSize="$4" color="$colorPress">
              <EMAIL>
            </Text>
            <Button size="$3" theme="active">
              {t('profile.editProfile')}
            </Button>
          </Card>

          {/* Account Settings */}
          <YStack space="$3">
            <Text fontSize="$5" fontWeight="bold">{t('profile.accountSettings')}</Text>
            
            <Card padding="$4">
              <XStack alignItems="center" space="$3" pressStyle={{ scale: 0.98 }}>
                <MaterialIcons name="person" size={24} color="#5889B6" />
                <YStack flex={1}>
                  <Text fontSize="$4">{t('profile.personalInformation')}</Text>
                  <Text fontSize="$3" color="$colorPress">
                    {t('profile.personalInfoDesc')}
                  </Text>
                </YStack>
                <MaterialIcons name="arrow-forward" size={20} color="#A3ADB8" />
              </XStack>
            </Card>

            <Card padding="$4">
              <XStack alignItems="center" space="$3" pressStyle={{ scale: 0.98 }}>
                <MaterialIcons name="security" size={24} color="#5889B6" />
                <YStack flex={1}>
                  <Text fontSize="$4">{t('profile.security')}</Text>
                  <Text fontSize="$3" color="$colorPress">
                    {t('profile.securityDesc')}
                  </Text>
                </YStack>
                <MaterialIcons name="arrow-forward" size={20} color="#A3ADB8" />
              </XStack>
            </Card>
          </YStack>

          {/* App Settings */}
          <YStack space="$3">
            <Text fontSize="$5" fontWeight="bold">{t('profile.appSettings')}</Text>
            
            <Dialog modal open={languageDialogOpen} onOpenChange={setLanguageDialogOpen}>
              <Dialog.Trigger asChild>
                <Card padding="$4" pressStyle={{ scale: 0.98 }}>
                  <XStack alignItems="center" space="$3">
                    <MaterialIcons name="language" size={24} color="#5889B6" />
                    <YStack flex={1}>
                      <Text fontSize="$4">{t('profile.language')}</Text>
                      <Text fontSize="$3" color="$colorPress">
                        {getCurrentLanguageName()}
                      </Text>
                    </YStack>
                    <MaterialIcons name="arrow-forward" size={20} color="#A3ADB8" />
                  </XStack>
                </Card>
              </Dialog.Trigger>

              <Adapt when="sm" platform="touch">
                <Sheet animation="medium" zIndex={200000} modal dismissOnSnapToBottom>
                  <Sheet.Frame padding="$4" gap="$4">
                    <Adapt.Contents />
                  </Sheet.Frame>
                  <Sheet.Overlay
                    animation="lazy"
                    enterStyle={{ opacity: 0 }}
                    exitStyle={{ opacity: 0 }}
                  />
                </Sheet>
              </Adapt>

              <Dialog.Portal>
                <Dialog.Overlay
                  key="overlay"
                  animation="slow"
                  opacity={0.5}
                  enterStyle={{ opacity: 0 }}
                  exitStyle={{ opacity: 0 }}
                />

                <Dialog.Content
                  bordered
                  elevate
                  key="content"
                  animateOnly={['transform', 'opacity']}
                  animation={[
                    'quicker',
                    {
                      opacity: {
                        overshootClamping: true,
                      },
                    },
                  ]}
                  enterStyle={{ x: 0, y: -20, opacity: 0, scale: 0.9 }}
                  exitStyle={{ x: 0, y: 10, opacity: 0, scale: 0.95 }}
                  gap="$4"
                  padding="$4"
                  maxWidth={400}
                >
                  <Dialog.Title fontSize="$6" fontWeight="bold">
                    {t('profile.language')}
                  </Dialog.Title>
                  <Dialog.Description fontSize="$4" color="$colorPress">
                    Select your preferred language
                  </Dialog.Description>

                  <YStack space="$2">
                    {languages.map((language) => (
                      <Card
                        key={language.code}
                        padding="$3"
                        pressStyle={{ scale: 0.98 }}
                        onPress={() => handleLanguageChange(language.code)}
                        backgroundColor={i18n.language === language.code ? '$backgroundPress' : '$background'}
                      >
                        <XStack alignItems="center" justifyContent="space-between">
                          <Text fontSize="$4">{language.name}</Text>
                          {i18n.language === language.code && (
                            <Check size={20} color="#0A88AF" />
                          )}
                        </XStack>
                      </Card>
                    ))}
                  </YStack>

                  <XStack alignSelf="flex-end" gap="$4">
                    <Dialog.Close displayWhenAdapted asChild>
                      <Button theme="alt2" aria-label="Close">
                        {t('common.close')}
                      </Button>
                    </Dialog.Close>
                  </XStack>
                </Dialog.Content>
              </Dialog.Portal>
            </Dialog>

            <Card padding="$4">
              <XStack alignItems="center" justifyContent="space-between">
                <XStack alignItems="center" space="$3">
                  <MaterialIcons name="dark-mode" size={24} color="#5889B6" />
                  <YStack>
                    <Text fontSize="$4">{t('profile.darkMode')}</Text>
                    <Text fontSize="$3" color="$colorPress">
                      {t('profile.darkModeDesc')}
                    </Text>
                  </YStack>
                </XStack>
                <Switch size="$3" checked={isDark} onCheckedChange={toggleTheme} />
              </XStack>
            </Card>
          </YStack>

          {/* Notification Settings */}
          <YStack space="$3">
            <Text fontSize="$5" fontWeight="bold">{t('profile.notifications')}</Text>
            
            {[
              { icon: 'notifications', title: t('profile.pushNotifications'), subtitle: t('profile.pushNotificationsDesc') },
              { icon: 'email', title: t('profile.emailNotifications'), subtitle: t('profile.emailNotificationsDesc') },
              { icon: 'sms', title: t('profile.smsNotifications'), subtitle: t('profile.smsNotificationsDesc') },
              { icon: 'chat', title: t('profile.whatsapp'), subtitle: t('profile.whatsappDesc') },
            ].map((item, index) => (
              <Card key={index} padding="$4">
                <XStack alignItems="center" justifyContent="space-between">
                  <XStack alignItems="center" space="$3">
                    <MaterialIcons name={item.icon as any} size={24} color="#5889B6" />
                    <YStack>
                      <Text fontSize="$4">{item.title}</Text>
                      <Text fontSize="$3" color="$colorPress">
                        {item.subtitle}
                      </Text>
                    </YStack>
                  </XStack>
                  <Switch size="$3" />
                </XStack>
              </Card>
            ))}
          </YStack>

          {/* Other Options */}
          <YStack space="$3">
            <Text fontSize="$5" fontWeight="bold">{t('profile.other')}</Text>
            
            <Card padding="$4">
              <XStack alignItems="center" space="$3" pressStyle={{ scale: 0.98 }}>
                <MaterialIcons name="help" size={24} color="#5889B6" />
                <YStack flex={1}>
                  <Text fontSize="$4">{t('profile.viewOnboarding')}</Text>
                  <Text fontSize="$3" color="$colorPress">
                    {t('profile.viewOnboardingDesc')}
                  </Text>
                </YStack>
                <MaterialIcons name="arrow-forward" size={20} color="#A3ADB8" />
              </XStack>
            </Card>

            <Card padding="$4">
              <XStack alignItems="center" space="$3" pressStyle={{ scale: 0.98 }}>
                <MaterialIcons name="info" size={24} color="#5889B6" />
                <YStack flex={1}>
                  <Text fontSize="$4">{t('profile.about')}</Text>
                  <Text fontSize="$3" color="$colorPress">
                    {t('profile.aboutDesc')}
                  </Text>
                </YStack>
                <MaterialIcons name="arrow-forward" size={20} color="#A3ADB8" />
              </XStack>
            </Card>

            <Card padding="$4">
              <XStack alignItems="center" space="$3" pressStyle={{ scale: 0.98 }}>
                <MaterialIcons name="logout" size={24} color="#d32f2f" />
                <Text fontSize="$4" color="#d32f2f">
                  {t('profile.signOut')}
                </Text>
              </XStack>
            </Card>
          </YStack>
        </YStack>
      </ScrollView>
    </SafeAreaView>
  );
}