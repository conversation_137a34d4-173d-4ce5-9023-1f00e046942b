import { useEffect } from 'react';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { TamaguiProvider, Theme } from '@tamagui/core';
import { PortalProvider } from '@tamagui/portal';
import { useFrameworkReady } from '@/hooks/useFrameworkReady';
import { useTheme } from '@/hooks/useTheme';
import config from '../tamagui.config';
import '../i18n';

export default function RootLayout() {
  useFrameworkReady();
  const { themeName } = useTheme();

  return (
    <TamaguiProvider config={config}>
      <Theme name={themeName}>
        <PortalProvider>
          <Stack screenOptions={{ headerShown: false }}>
            <Stack.Screen name="(auth)" />
            <Stack.Screen name="(onboarding)" />
            <Stack.Screen name="(tabs)" />
            <Stack.Screen name="+not-found" />
          </Stack>
          <StatusBar style="auto" />
        </PortalProvider>
      </Theme>
    </TamaguiProvider>
  );
}