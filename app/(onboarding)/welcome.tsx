import React, { useState } from 'react';
import { Dimensions } from 'react-native';
import { router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { YStack, XStack, Text, Button, Circle } from 'tamagui';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/hooks/useTheme';

const { width } = Dimensions.get('window');

const onboardingData = [
  {
    icon: 'folder',
    title: 'Manage Your Assets',
    description: 'Securely store and organize your digital assets with ease',
  },
  {
    icon: 'people',
    title: 'Connect with Contacts',
    description: 'Share and manage access to your assets with trusted contacts',
  },
  {
    icon: 'timeline',
    title: 'Your Youlogy Journey',
    description: 'Create your digital legacy and preserve precious memories',
  },
];

export default function WelcomeScreen() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const { backgroundColor } = useTheme();

  const handleNext = () => {
    if (currentIndex < onboardingData.length - 1) {
      setCurrentIndex(currentIndex + 1);
    } else {
      router.push('/(tabs)');
    }
  };

  const handleSkip = () => {
    router.push('/(tabs)');
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor }}>
      <YStack flex={1} padding="$4">
        {/* Skip Button */}
        <XStack justifyContent="flex-end" marginBottom="$4">
          <Button onPress={handleSkip} chromeless>
            <Text color="$primary">Skip</Text>
          </Button>
        </XStack>

        {/* Content */}
        <YStack flex={1} justifyContent="center" alignItems="center" space="$6">
          <MaterialIcons 
            name={onboardingData[currentIndex].icon as any} 
            size={120} 
            color="#0A88AF" 
          />
          
          <YStack alignItems="center" space="$3">
            <Text fontSize="$8" fontWeight="bold" textAlign="center" color="$primary">
              {onboardingData[currentIndex].title}
            </Text>
            <Text fontSize="$5" textAlign="center" color="$color" maxWidth={300}>
              {onboardingData[currentIndex].description}
            </Text>
          </YStack>
        </YStack>

        {/* Indicators */}
        <XStack justifyContent="center" space="$2" marginBottom="$4">
          {onboardingData.map((_, index) => (
            <Circle
              key={index}
              size="$1"
              backgroundColor={index === currentIndex ? '$primary' : '$borderColor'}
            />
          ))}
        </XStack>

        {/* Next Button */}
        <Button size="$4" theme="active" onPress={handleNext}>
          {currentIndex === onboardingData.length - 1 ? 'Get Started' : 'Next'}
        </Button>

        {/* Don't show again option */}
        <XStack justifyContent="center" marginTop="$4">
          <Text fontSize="$3" color="$colorPress">
            Don't show this again
          </Text>
        </XStack>
      </YStack>
    </SafeAreaView>
  );
}