import React from 'react';
import { Link, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { YStack, XStack, Text, Input, Button, Separator } from 'tamagui';
import { MaterialIcons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/hooks/useTheme';

export default function LoginScreen() {
  const { t } = useTranslation();
  const { backgroundColor } = useTheme();

  const handleLogin = () => {
    // In real app, handle authentication
    router.push('/(onboarding)/welcome');
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor }}>
      <YStack flex={1} padding="$4" justifyContent="center" space="$4">
        {/* Logo */}
        <YStack alignItems="center" marginBottom="$6">
          <MaterialIcons name="account-circle" size={80} color="#0A88AF" />
          <Text fontSize="$8" fontWeight="bold" color="$primary" marginTop="$2">
            {t('app.name')}
          </Text>
        </YStack>

        {/* Login Form */}
        <YStack space="$4">
          <YStack>
            <Text fontSize="$4" marginBottom="$2">{t('auth.email')}</Text>
            <Input
              placeholder={t('auth.enterEmail')}
              keyboardType="email-address"
              autoCapitalize="none"
              size="$4"
            />
          </YStack>

          <YStack>
            <Text fontSize="$4" marginBottom="$2">{t('auth.password')}</Text>
            <Input
              placeholder={t('auth.enterPassword')}
              secureTextEntry
              size="$4"
            />
          </YStack>

          <Button
            size="$4"
            theme="active"
            onPress={handleLogin}
            marginTop="$4"
          >
            {t('auth.login')}
          </Button>
        </YStack>

        {/* Links */}
        <YStack space="$3" marginTop="$6">
          <XStack justifyContent="center">
            <Link href="/(auth)/forgot-password" asChild>
              <Text color="$primary" textDecorationLine="underline">
                {t('auth.forgotPassword')}
              </Text>
            </Link>
          </XStack>
          
          <Separator />
          
          <XStack justifyContent="center" space="$2">
            <Text>{t('auth.dontHaveAccount')}</Text>
            <Link href="/(auth)/register" asChild>
              <Text color="$primary" fontWeight="bold">
                {t('auth.register')}
              </Text>
            </Link>
          </XStack>
        </YStack>
      </YStack>
    </SafeAreaView>
  );
}