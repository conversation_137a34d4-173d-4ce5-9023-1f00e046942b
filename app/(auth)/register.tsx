import React from 'react';
import { Link, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { YStack, XStack, Text, Input, Button, ScrollView } from 'tamagui';
import { MaterialIcons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';

export default function RegisterScreen() {
  const { t } = useTranslation();

  const handleRegister = () => {
    // In real app, handle registration
    router.push('/(onboarding)/welcome');
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '$background' }}>
      <ScrollView flex={1}>
        <YStack flex={1} padding="$4" justifyContent="center" space="$4">
          {/* Logo */}
          <YStack alignItems="center" marginBottom="$4" marginTop="$4">
            <MaterialIcons name="account-circle" size={60} color="#0A88AF" />
            <Text fontSize="$6" fontWeight="bold" color="$primary" marginTop="$2">
              {t('auth.register')}
            </Text>
          </YStack>

          {/* Register Form */}
          <YStack space="$4">
            <XStack space="$2">
              <YStack flex={1}>
                <Text fontSize="$4" marginBottom="$2">{t('auth.firstName')}</Text>
                <Input placeholder={t('auth.enterFirstName')} size="$4" />
              </YStack>
              <YStack flex={1}>
                <Text fontSize="$4" marginBottom="$2">{t('auth.lastName')}</Text>
                <Input placeholder={t('auth.enterLastName')} size="$4" />
              </YStack>
            </XStack>

            <YStack>
              <Text fontSize="$4" marginBottom="$2">{t('auth.mobileNumber')}</Text>
              <Input
                placeholder={t('auth.enterMobileNumber')}
                keyboardType="phone-pad"
                size="$4"
              />
            </YStack>

            <YStack>
              <Text fontSize="$4" marginBottom="$2">{t('auth.email')}</Text>
              <Input
                placeholder={t('auth.enterEmail')}
                keyboardType="email-address"
                autoCapitalize="none"
                size="$4"
              />
            </YStack>

            <YStack>
              <Text fontSize="$4" marginBottom="$2">{t('auth.password')}</Text>
              <Input
                placeholder={t('auth.enterPassword')}
                secureTextEntry
                size="$4"
              />
            </YStack>

            <YStack>
              <Text fontSize="$4" marginBottom="$2">{t('auth.confirmPassword')}</Text>
              <Input
                placeholder={t('auth.confirmYourPassword')}
                secureTextEntry
                size="$4"
              />
            </YStack>

            <Button
              size="$4"
              theme="active"
              onPress={handleRegister}
              marginTop="$4"
            >
              {t('auth.register')}
            </Button>
          </YStack>

          {/* Login Link */}
          <XStack justifyContent="center" space="$2" marginTop="$6" marginBottom="$4">
            <Text>{t('auth.alreadyHaveAccount')}</Text>
            <Link href="/(auth)/login" asChild>
              <Text color="$primary" fontWeight="bold">
                {t('auth.login')}
              </Text>
            </Link>
          </XStack>
        </YStack>
      </ScrollView>
    </SafeAreaView>
  );
}