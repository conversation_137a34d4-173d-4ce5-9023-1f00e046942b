import React from 'react';
import { Link, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { YStack, XStack, Text, Input, Button } from 'tamagui';
import { MaterialIcons } from '@expo/vector-icons';

export default function ResetPasswordScreen() {
  const handleReset = () => {
    // In real app, handle password reset
    router.push('/(auth)/login');
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: '$background' }}>
      <YStack flex={1} padding="$4" justifyContent="center" space="$4">
        {/* Logo */}
        <YStack alignItems="center" marginBottom="$6">
          <MaterialIcons name="security" size={80} color="#0A88AF" />
          <Text fontSize="$7" fontWeight="bold" color="$primary" marginTop="$2">
            Reset Password
          </Text>
          <Text fontSize="$4" textAlign="center" color="$color" marginTop="$2">
            Enter your new password
          </Text>
        </YStack>

        {/* Form */}
        <YStack space="$4">
          <YStack>
            <Text fontSize="$4" marginBottom="$2">New Password</Text>
            <Input
              placeholder="Enter new password"
              secureTextEntry
              size="$4"
            />
          </YStack>

          <YStack>
            <Text fontSize="$4" marginBottom="$2">Confirm Password</Text>
            <Input
              placeholder="Confirm new password"
              secureTextEntry
              size="$4"
            />
          </YStack>

          <Button
            size="$4"
            theme="active"
            onPress={handleReset}
            marginTop="$4"
          >
            Reset Password
          </Button>
        </YStack>

        {/* Back to Login */}
        <XStack justifyContent="center" marginTop="$6">
          <Link href="/(auth)/login" asChild>
            <Text color="$primary" textDecorationLine="underline">
              Back to Login
            </Text>
          </Link>
        </XStack>
      </YStack>
    </SafeAreaView>
  );
}