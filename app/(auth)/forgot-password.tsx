import React from 'react';
import { Link, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { YStack, XStack, Text, Input, Button } from 'tamagui';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/hooks/useTheme';

export default function ForgotPasswordScreen() {
  const { backgroundColor } = useTheme();

  const handleSubmit = () => {
    // In real app, handle password reset request
    router.push('/(auth)/reset-password');
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor }}>
      <YStack flex={1} padding="$4" justifyContent="center" space="$4">
        {/* Logo */}
        <YStack alignItems="center" marginBottom="$6">
          <MaterialIcons name="lock-reset" size={80} color="#0A88AF" />
          <Text fontSize="$7" fontWeight="bold" color="$primary" marginTop="$2">
            Forgot Password
          </Text>
          <Text fontSize="$4" textAlign="center" color="$color" marginTop="$2">
            Enter your email to receive reset instructions
          </Text>
        </YStack>

        {/* Form */}
        <YStack space="$4">
          <YStack>
            <Text fontSize="$4" marginBottom="$2">Email</Text>
            <Input
              placeholder="Enter your email"
              keyboardType="email-address"
              autoCapitalize="none"
              size="$4"
            />
          </YStack>

          <Button
            size="$4"
            theme="active"
            onPress={handleSubmit}
            marginTop="$4"
          >
            Submit
          </Button>
        </YStack>

        {/* Back to Login */}
        <XStack justifyContent="center" marginTop="$6">
          <Link href="/(auth)/login" asChild>
            <Text color="$primary" textDecorationLine="underline">
              Back to Login
            </Text>
          </Link>
        </XStack>
      </YStack>
    </SafeAreaView>
  );
}